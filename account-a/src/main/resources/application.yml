server:
  port: 8081

spring:
  application:
    name: account-a
  datasource:
    url: *************************************
    username: your_user
    password: your_pwd
    driver-class-name: oracle.jdbc.OracleDriver

seata:
  tx-service-group: my_tx_group
  registry:
    type: nacos
    nacos:
      server-addr: 127.0.0.1:8848
      group: DEFAULT_GROUP
      namespace: ""
  config:
    type: nacos
    nacos:
      server-addr: 127.0.0.1:8848
      group: DEFAULT_GROUP
      namespace: ""
  service:
    vgroup-mapping:
      my_tx_group: DEFAULT_GROUP
    grouplist:
      default: 127.0.0.1:8091
