package com.example;

import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.UUID;

@Slf4j
@Service
public class TransferService {

    @Autowired
    private AccountTccAction accountTccActionA; // 本地

    @Autowired
    private AccountBClient accountBClient; // 远程调用

    @GlobalTransactional(name = "account-tcc-tx")
    public void transfer(String fromUser, String toUser, BigDecimal amount) {
        String txId = UUID.randomUUID().toString();
        log.info("全局事务开始 txId = {}", txId);

        // 发起本地 prepare
        accountTccActionA.prepare(fromUser, amount, txId);

        // 发起远程 prepare（account-b 的参与者）
        accountBClient.prepare(toUser, amount.negate(), txId);
    }
}
