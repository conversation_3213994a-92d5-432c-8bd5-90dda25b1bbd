package com.example;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequestMapping("/tcc")
public class TccController {

    @Autowired
    private TransferService transferService;

    @PostMapping("/prepare")
    public String prepare(@RequestParam String fromUser, @RequestParam BigDecimal amount, @RequestParam String toUser) {
        transferService.transfer(fromUser, toUser, amount);
        return "ok";
    }
}
