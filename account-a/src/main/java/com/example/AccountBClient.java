package com.example;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;

@FeignClient(name = "account-b", path = "/tcc")
public interface AccountBClient {
    @PostMapping("/prepare")
    String prepare(@RequestParam String user, @RequestParam BigDecimal amount, @RequestParam String txId);
}
