package com.example;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

/**
 * {@code @date} 2025-7-31 23:33:13
 */
@Repository
@Slf4j
public class AccountRepository {

    public boolean freezeAmount(String userId, BigDecimal amount) {
        // 模拟冻结金额：UPDATE account SET frozen_amount = frozen_amount + ? WHERE user_id = ?
        log.info("{}[freezeAmount] userId={}, amount={}", this.getClass().getSimpleName(), userId, amount);
        return true;
    }

    public boolean confirmDeduct(String userId, BigDecimal amount) {
        // 模拟扣款：UPDATE account SET balance = balance - ?, frozen_amount = frozen_amount - ? WHERE user_id = ?
        log.info("{}[confirmDeduct] userId={}, amount={}", this.getClass().getSimpleName(), userId, amount);
        return true;
    }

    public boolean rollbackFreeze(String userId, BigDecimal amount) {
        // 模拟回滚冻结金额：UPDATE account SET frozen_amount = frozen_amount - ? WHERE user_id = ?
        log.info("{}[rollbackFreeze] userId={}, amount={}", this.getClass().getSimpleName(), userId, amount);
        return true;
    }
}
