package com.example;

import io.seata.rm.tcc.api.BusinessActionContext;
import io.seata.rm.tcc.api.LocalTCC;
import io.seata.rm.tcc.api.TwoPhaseBusinessAction;
import io.seata.rm.tcc.api.BusinessActionContextParameter;

import java.math.BigDecimal;

@LocalTCC
public interface AccountTccAction {

    @TwoPhaseBusinessAction(name = "accountTccAction", commitMethod = "commit", rollbackMethod = "cancel")
    boolean prepare(@BusinessActionContextParameter("userId") String userId,
                    @BusinessActionContextParameter("amount") BigDecimal amount,
                    @BusinessActionContextParameter("txId") String txId);

    boolean commit(BusinessActionContext ctx);
    boolean cancel(BusinessActionContext ctx);
}
