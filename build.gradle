plugins {
    id 'java'
    id 'org.springframework.boot' version '2.6.15' apply false
    id 'io.spring.dependency-management' version '1.0.15.RELEASE' apply false
}

allprojects {
    repositories {
        mavenCentral()
    }
}

subprojects {
    apply plugin: 'java'
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'

    java {
        sourceCompatibility = JavaVersion.VERSION_1_8
    }

    dependencies {

        implementation platform("org.springframework.cloud:spring-cloud-dependencies:2021.0.6")

        implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
        implementation 'org.springframework.cloud:spring-cloud-starter-loadbalancer'

        implementation 'org.springframework.boot:spring-boot-starter-web'
        implementation 'org.springframework.boot:spring-boot-starter-aop'
        implementation 'io.seata:seata-spring-boot-starter:1.5.2'
        implementation 'io.seata:seata-tcc:1.5.2'
        implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery:2021.0.5.0'

        runtimeOnly 'com.oracle.database.jdbc:ojdbc8:19.3.0.0'
        compileOnly 'org.projectlombok:lombok'
        annotationProcessor 'org.projectlombok:lombok'
        testImplementation 'org.springframework.boot:spring-boot-starter-test'
    }

    test {
        useJUnitPlatform()
    }
}
