-- 用户账户表
CREATE TABLE account (
    user_id VARCHAR2(64) PRIMARY KEY,
    balance NUMBER(18, 2) NOT NULL,
    frozen_amount NUMBER(18, 2) DEFAULT 0 NOT NULL
);

-- Seata undo log 表（AT 模式下使用，TCC 不强制，但建议保留）
CREATE TABLE undo_log (
    id NUMBER GENERATED BY DEFAULT ON NULL AS IDENTITY PRIMARY KEY,
    branch_id NUMBER NOT NULL,
    xid VARCHAR2(100) NOT NULL,
    context VARCHAR2(128),
    rollback_info BLOB NOT NULL,
    log_status NUMBER NOT NULL,
    log_created TIMESTAMP NOT NULL,
    log_modified TIMESTAMP NOT NULL
);
