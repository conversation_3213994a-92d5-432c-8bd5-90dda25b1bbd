package com.example;

import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.UUID;

@Service
public class TransferService {

    @Autowired
    private AccountTccAction accountTccAction;

    @GlobalTransactional
    public void transfer(String fromUser, String toUser, BigDecimal amount) {
        String txId = UUID.randomUUID().toString();
        accountTccAction.prepare(fromUser, amount, txId);
        accountTccAction.prepare(toUser, amount.negate(), txId); // 模拟跨服务调用
    }
}
