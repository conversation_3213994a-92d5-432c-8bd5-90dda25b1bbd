package com.example;

import io.seata.rm.tcc.api.BusinessActionContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Slf4j
@Service
public class AccountTccActionImpl implements AccountTccAction {

    @Override
    public boolean prepare(String userId, BigDecimal amount, String txId) {
        log.info("[Try] userId={}, amount={}, txId={}", userId, amount, txId);
        // 预扣资金逻辑：更新冻结金额
        return true;
    }

    @Override
    public boolean commit(BusinessActionContext ctx) {
        String userId = (String) ctx.getActionContext("userId");
        BigDecimal amount = new BigDecimal(ctx.getActionContext("amount").toString());
        log.info("[Confirm] userId={}, amount={}", userId, amount);
        // 扣除冻结金额，真正扣款
        return true;
    }

    @Override
    public boolean cancel(BusinessActionContext ctx) {
        String userId = (String) ctx.getActionContext("userId");
        BigDecimal amount = new BigDecimal(ctx.getActionContext("amount").toString());
        log.info("[Cancel] userId={}, amount={}", userId, amount);
        // 释放冻结金额
        return true;
    }
}
